#include "Minimap.h"
#include <cmath>
#include <algorithm>

Minimap::Minimap(int screenW, int screenH)
    : screenWidth(screenW), screenHeight(screenH),
      scale(0.02f), maxDistance(100.0f), enabled(true) {

    // Tamanho do minimapa (canto superior direito)
    mapWidth = 200;
    mapHeight = 200;
    mapX = screenWidth - mapWidth - 20;
    mapY = screenHeight - mapHeight - 20;
}

Minimap::~Minimap() {
}

void Minimap::updateScreenSize(int screenW, int screenH) {
    screenWidth = screenW;
    screenHeight = screenH;
    mapX = screenWidth - mapWidth - 20;
    mapY = screenHeight - mapHeight - 20;
}

void Minimap::render(const Camera& camera, const std::vector<Asteroid>& asteroids, const Sun& sun) {
    if (!enabled) return;

    // Salva estados atuais
    GLboolean lightingEnabled = glIsEnabled(GL_LIGHTING);
    GLboolean depthTestEnabled = glIsEnabled(GL_DEPTH_TEST);
    GLboolean colorMaterialEnabled = glIsEnabled(GL_COLOR_MATERIAL);

    setupOrthographicProjection();

    glDisable(GL_DEPTH_TEST);
    glDisable(GL_LIGHTING);
    glDisable(GL_COLOR_MATERIAL); // Desabilita color material para cores diretas

    renderBackground();
    renderGrid();
    renderSun(sun, camera.position);
    renderAsteroids(asteroids, camera.position);
    renderCamera(camera);
    renderBorder();

    // Restaura estados salvos
    if (lightingEnabled) glEnable(GL_LIGHTING);
    if (depthTestEnabled) glEnable(GL_DEPTH_TEST);
    if (colorMaterialEnabled) glEnable(GL_COLOR_MATERIAL);

    restorePerspectiveProjection();
}

void Minimap::setupOrthographicProjection() {
    glMatrixMode(GL_PROJECTION);
    glPushMatrix();
    glLoadIdentity();
    glOrtho(0, screenWidth, 0, screenHeight, -1, 1);

    glMatrixMode(GL_MODELVIEW);
    glPushMatrix();
    glLoadIdentity();
}

void Minimap::restorePerspectiveProjection() {
    glMatrixMode(GL_PROJECTION);
    glPopMatrix();

    glMatrixMode(GL_MODELVIEW);
    glPopMatrix();
}

void Minimap::renderBackground() {
    // Fundo semi-transparente
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    glColor4f(0.0f, 0.0f, 0.1f, 0.8f);
    glBegin(GL_QUADS);
    glVertex2i(mapX, mapY);
    glVertex2i(mapX + mapWidth, mapY);
    glVertex2i(mapX + mapWidth, mapY + mapHeight);
    glVertex2i(mapX, mapY + mapHeight);
    glEnd();

    glDisable(GL_BLEND);
}

void Minimap::renderGrid() {
    glColor3f(0.2f, 0.2f, 0.3f);
    glBegin(GL_LINES);

    // Linhas verticais
    for (int i = 0; i <= 10; i++) {
        int x = mapX + (i * mapWidth) / 10;
        glVertex2i(x, mapY);
        glVertex2i(x, mapY + mapHeight);
    }

    // Linhas horizontais
    for (int i = 0; i <= 10; i++) {
        int y = mapY + (i * mapHeight) / 10;
        glVertex2i(mapX, y);
        glVertex2i(mapX + mapWidth, y);
    }

    glEnd();
}

void Minimap::renderCamera(const Camera& camera) {
    int centerX = mapX + mapWidth / 2;
    int centerY = mapY + mapHeight / 2;

    // Posição da câmera (sempre no centro)
    glColor3f(0.0f, 1.0f, 0.0f);
    glPointSize(6.0f);
    glBegin(GL_POINTS);
    glVertex2i(centerX, centerY);
    glEnd();

    // Direção da câmera
    glColor3f(0.0f, 0.8f, 0.0f);
    glBegin(GL_LINES);
    glVertex2i(centerX, centerY);

    int dirX = centerX + (int)(camera.direction.x * 20);
    int dirY = centerY + (int)(camera.direction.z * 20);
    glVertex2i(dirX, dirY);
    glEnd();

    glPointSize(1.0f);
}

void Minimap::renderSun(const Sun& sun, const Vector3& cameraPos) {
    if (!isInRange(sun.getPosition(), cameraPos)) return;

    int sunX, sunY;
    worldToMinimap(sun.getPosition(), cameraPos, sunX, sunY);

    // Renderiza o sol como um círculo amarelo
    glColor3f(1.0f, 1.0f, 0.0f);
    glPointSize(8.0f);
    glBegin(GL_POINTS);
    glVertex2i(sunX, sunY);
    glEnd();

    // Halo ao redor do sol
    glColor3f(1.0f, 0.8f, 0.0f);
    glPointSize(12.0f);
    glBegin(GL_POINTS);
    glVertex2i(sunX, sunY);
    glEnd();

    glPointSize(1.0f);
}

void Minimap::renderAsteroids(const std::vector<Asteroid>& asteroids, const Vector3& cameraPos) {
    glPointSize(3.0f);

    for (const auto& asteroid : asteroids) {
        if (!isInRange(asteroid.position, cameraPos)) continue;

        int astX, astY;
        worldToMinimap(asteroid.position, cameraPos, astX, astY);

        // Cor baseada no tipo de asteroide
        if (asteroid.useProceduralMesh) {
            glColor3f(0.8f, 0.6f, 0.4f); // Marrom para procedurais
        } else {
            glColor3f(0.6f, 0.6f, 0.8f); // Azul acinzentado para clássicos
        }

        glBegin(GL_POINTS);
        glVertex2i(astX, astY);
        glEnd();
    }

    glPointSize(1.0f);
}

void Minimap::renderBorder() {
    glColor3f(0.5f, 0.5f, 0.7f);
    glBegin(GL_LINE_LOOP);
    glVertex2i(mapX, mapY);
    glVertex2i(mapX + mapWidth, mapY);
    glVertex2i(mapX + mapWidth, mapY + mapHeight);
    glVertex2i(mapX, mapY + mapHeight);
    glEnd();
}

void Minimap::worldToMinimap(const Vector3& worldPos, const Vector3& cameraPos, int& mapPosX, int& mapPosY) {
    // Calcula posição relativa à câmera
    float relX = worldPos.x - cameraPos.x;
    float relZ = worldPos.z - cameraPos.z;

    // Converte para coordenadas do minimapa
    int centerX = mapX + mapWidth / 2;
    int centerY = mapY + mapHeight / 2;

    mapPosX = centerX + (int)(relX * scale * mapWidth);
    mapPosY = centerY + (int)(relZ * scale * mapHeight);

    // Limita às bordas do minimapa
    mapPosX = std::max(mapX, std::min(mapX + mapWidth, mapPosX));
    mapPosY = std::max(mapY, std::min(mapY + mapHeight, mapPosY));
}

bool Minimap::isInRange(const Vector3& worldPos, const Vector3& cameraPos) {
    float distance = (worldPos - cameraPos).length();
    return distance <= maxDistance;
}
