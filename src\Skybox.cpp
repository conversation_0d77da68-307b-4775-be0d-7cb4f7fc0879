#include "Skybox.h"
#include <cstdlib>
#include <cmath>
#include <iostream>

// Fallback para sistemas que não têm GL_CLAMP_TO_EDGE
#ifndef GL_CLAMP_TO_EDGE
#define GL_CLAMP_TO_EDGE 0x812F
#endif

Skybox::Skybox() : enabled(true), textureManager(nullptr) {
    for (int i = 0; i < 6; i++) {
        skyboxTextures[i] = 0;
    }
}

Skybox::~Skybox() {
    cleanup();
}

bool Skybox::initialize(TextureManager* texManager) {
    textureManager = texManager;  // Mantém para compatibilidade, mas não usa mais
    return loadSkyboxTextures();
}

void Skybox::cleanup() {
    for (int i = 0; i < 6; i++) {
        if (skyboxTextures[i] != 0) {
            glDeleteTextures(1, &skyboxTextures[i]);
            skyboxTextures[i] = 0;
        }
    }
}

void Skybox::render(const Camera& camera) {
    if (!enabled) return;

    // Salva estados atuais
    GLboolean lightingEnabled = glIsEnabled(GL_LIGHTING);
    GLboolean depthTestEnabled = glIsEnabled(GL_DEPTH_TEST);
    GLboolean textureEnabled = glIsEnabled(GL_TEXTURE_2D);
    GLboolean cullFaceEnabled = glIsEnabled(GL_CULL_FACE);
    GLboolean blendEnabled = glIsEnabled(GL_BLEND);

    // Configura estados para skybox - renderiza sempre no fundo
    glDisable(GL_LIGHTING);
    glDisable(GL_BLEND);
    glDisable(GL_TEXTURE_2D);  // Remove texturas para fazer cubo simples
    glDepthFunc(GL_LEQUAL);    // Permite que skybox seja renderizado no fundo
    glDepthMask(GL_FALSE);     // Não escreve no depth buffer
    glDisable(GL_CULL_FACE);   // Garante que todas as faces sejam visíveis

    glPushMatrix();
    glTranslatef(camera.position.x, camera.position.y, camera.position.z);

    // Cor azul escura para simular espaço - mesma cor do background
    glColor4f(0.05f, 0.05f, 0.1f, 1.0f);

    renderSkyboxCube();

    glPopMatrix();

    // Restaura estados
    glDepthMask(GL_TRUE);
    glDepthFunc(GL_LESS);  // Restaura função de depth padrão
    if (depthTestEnabled) glEnable(GL_DEPTH_TEST);
    if (lightingEnabled) glEnable(GL_LIGHTING);
    if (textureEnabled) glEnable(GL_TEXTURE_2D);
    if (cullFaceEnabled) glEnable(GL_CULL_FACE);
    if (blendEnabled) glEnable(GL_BLEND);
}

bool Skybox::loadSkyboxTextures() {
    // Skybox agora é um cubo simples sem texturas
    std::cout << "Skybox: Inicializado como cubo simples sem texturas" << std::endl;

    // Limpa qualquer textura existente
    for (int i = 0; i < 6; i++) {
        skyboxTextures[i] = 0;
    }

    return true;
}



void Skybox::renderSkyboxCube() {
    float size = 1000.0f;  // Cubo maior para garantir que sempre envolva a cena

    // Front face (Z = -size)
    glBegin(GL_QUADS);
    glVertex3f(-size, -size, -size);
    glVertex3f( size, -size, -size);
    glVertex3f( size,  size, -size);
    glVertex3f(-size,  size, -size);
    glEnd();

    // Back face (Z = +size)
    glBegin(GL_QUADS);
    glVertex3f( size, -size,  size);
    glVertex3f(-size, -size,  size);
    glVertex3f(-size,  size,  size);
    glVertex3f( size,  size,  size);
    glEnd();

    // Left face (X = -size)
    glBegin(GL_QUADS);
    glVertex3f(-size, -size,  size);
    glVertex3f(-size, -size, -size);
    glVertex3f(-size,  size, -size);
    glVertex3f(-size,  size,  size);
    glEnd();

    // Right face (X = +size)
    glBegin(GL_QUADS);
    glVertex3f( size, -size, -size);
    glVertex3f( size, -size,  size);
    glVertex3f( size,  size,  size);
    glVertex3f( size,  size, -size);
    glEnd();

    // Top face (Y = +size)
    glBegin(GL_QUADS);
    glVertex3f(-size,  size, -size);
    glVertex3f( size,  size, -size);
    glVertex3f( size,  size,  size);
    glVertex3f(-size,  size,  size);
    glEnd();

    // Bottom face (Y = -size)
    glBegin(GL_QUADS);
    glVertex3f(-size, -size,  size);
    glVertex3f( size, -size,  size);
    glVertex3f( size, -size, -size);
    glVertex3f(-size, -size, -size);
    glEnd();
}

void Skybox::debugTextureStatus() const {
    std::cout << "=== DEBUG SKYBOX SIMPLES ===" << std::endl;
    std::cout << "Skybox configurado como cubo simples sem texturas" << std::endl;
    std::cout << "Cor: Azul escuro (0.05, 0.05, 0.2)" << std::endl;
    std::cout << "Tamanho: 1000x1000x1000 unidades" << std::endl;
    std::cout << "============================" << std::endl;
}


